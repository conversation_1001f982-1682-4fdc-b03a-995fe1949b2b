'use client'

import Link from 'next/link'
import { useState } from 'react'
import {
  Shield,
  Database,
  BarChart3,
  Users,
  Building2,
  Globe,
  ArrowRight,
  CheckCircle,
  Monitor,
  FileText,
  GitMerge,
  Server,
  Network,
  HardDrive,
  Sparkles,
  Award,
  Zap,
  TrendingUp,
  Eye,
  Lock,
  Cpu
} from 'lucide-react'

export default function HomePage() {
  const [showModuleOverlay, setShowModuleOverlay] = useState(false)
  const [showLifecycleModules, setShowLifecycleModules] = useState(false)
  const [showOpsModules, setShowOpsModules] = useState(false)

  // 业务模块数据
  const businessModules = [
    {
      name: '数据大屏',
      description: '实时数据可视化展示，支持多种图表和指标监控',
      href: '/screen',
      icon: Monitor,
      gradient: 'from-blue-500 to-cyan-500',
      logo: '📊',
      features: ['实时监控', '可视化图表', '大屏展示']
    },
    {
      name: '报表分析',
      description: '多维度数据分析报表，支持自定义报表生成',
      href: '/reports',
      icon: FileText,
      gradient: 'from-green-500 to-emerald-500',
      logo: '📈',
      features: ['多维分析', '自定义报表', '数据导出']
    },
    {
      name: '数据采集',
      description: '多源数据采集接入，支持实时和批量数据导入',
      href: '/collection',
      icon: Database,
      gradient: 'from-purple-500 to-violet-500',
      logo: '🔄',
      features: ['多源接入', '实时采集', '数据清洗']
    },
    {
      name: '数据汇聚',
      description: '跨系统数据整合汇聚，统一数据标准和格式',
      href: '/aggregation',
      icon: GitMerge,
      gradient: 'from-orange-500 to-amber-500',
      logo: '🔗',
      features: ['数据整合', '标准化', '质量控制']
    },
    {
      name: '数据治理',
      description: '数据质量管控，数据清洗、去重、标准化处理',
      href: '/governance',
      icon: Shield,
      gradient: 'from-red-500 to-rose-500',
      logo: '🛡️',
      features: ['质量管控', '数据清洗', '合规管理']
    },
    {
      name: '资源池管理',
      description: '数据资源统一管理，资源目录和权限控制',
      href: '/resources',
      icon: HardDrive,
      gradient: 'from-indigo-500 to-blue-500',
      logo: '💾',
      features: ['资源目录', '权限管理', '存储优化']
    },
    {
      name: '设备监控',
      description: '服务器设备实时监控，性能指标和告警管理',
      href: '/monitoring',
      icon: Server,
      gradient: 'from-teal-500 to-cyan-500',
      logo: '🖥️',
      features: ['实时监控', '性能分析', '告警管理']
    },
    {
      name: '网络管理',
      description: '多物理网络管理，网络拓扑和流量监控',
      href: '/network',
      icon: Network,
      gradient: 'from-pink-500 to-rose-500',
      logo: '🌐',
      features: ['网络拓扑', '流量监控', '安全管理']
    }
  ]

  // 核心能力
  const capabilities = [
    {
      icon: Database,
      title: "数据全生命周期管理",
      description: "从数据采集、清洗、存储到应用的全流程管理，确保数据质量和安全性",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: BarChart3,
      title: "智能数据分析",
      description: "多维度数据分析和可视化展示，支持实时监控和预测分析",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: Shield,
      title: "数据安全治理",
      description: "完善的数据安全体系和质量管控机制，保障数据合规使用",
      gradient: "from-purple-500 to-violet-500"
    },
    {
      icon: Globe,
      title: "跨部门数据共享",
      description: "打破数据孤岛，实现政务数据统一共享和协同应用",
      gradient: "from-orange-500 to-red-500"
    },
    {
      icon: Cpu,
      title: "智能运维监控",
      description: "全方位的系统运维监控，确保平台稳定高效运行",
      gradient: "from-teal-500 to-cyan-500"
    }
  ]

  // 解决的场景
  const scenarios = [
    {
      title: "政府决策支撑",
      description: "为领导层提供实时、准确的数据分析，支撑科学决策",
      icon: TrendingUp
    },
    {
      title: "跨部门协同",
      description: "消除信息孤岛，实现部门间数据无缝对接和业务协同",
      icon: Globe
    },
    {
      title: "公共服务优化",
      description: "通过数据分析优化公共服务流程，提升市民满意度",
      icon: Users
    },
    {
      title: "监管效能提升",
      description: "利用大数据技术提升监管效率和精准度",
      icon: Eye
    }
  ]

  // 数据来源单位
  const dataSourceUnits = [
    "市政府办公厅", "发展改革委", "教育局", "科技局", "工信局", "公安局",
    "民政局", "司法局", "财政局", "人社局", "自然资源局", "生态环境局",
    "住建局", "交通运输局", "水务局", "农业农村局", "商务局", "文旅局",
    "卫健委", "应急管理局", "审计局", "市场监管局", "统计局", "医保局"
  ]

  // 权威数据领域
  const authorityData = [
    { area: "人口信息", coverage: "100%", source: "公安、民政、人社等部门", color: "blue" },
    { area: "企业信息", coverage: "100%", source: "市场监管、税务、工信等部门", color: "green" },
    { area: "地理信息", coverage: "100%", source: "自然资源、住建、交通等部门", color: "purple" },
    { area: "经济数据", coverage: "95%", source: "统计、财政、发改等部门", color: "orange" },
    { area: "社会事业", coverage: "90%", source: "教育、卫健、文旅等部门", color: "pink" },
    { area: "环境数据", coverage: "100%", source: "生态环境、水务、应急等部门", color: "teal" }
  ]

  // 目标客户
  const targetUsers = [
    { 
      type: "政府决策层", 
      desc: "为各级领导提供数据支撑和分析报告，辅助科学决策",
      icon: Award,
      features: ["实时数据大屏", "决策分析报告", "趋势预测分析"]
    },
    { 
      type: "业务部门", 
      desc: "各委办局日常业务数据管理和跨部门协同应用",
      icon: Building2,
      features: ["业务数据管理", "跨部门协同", "流程优化"]
    },
    { 
      type: "技术人员", 
      desc: "数据开发、运维和技术支持人员的专业工具",
      icon: Cpu,
      features: ["数据开发工具", "系统监控", "技术支持"]
    },
    { 
      type: "公众服务", 
      desc: "为市民和企业提供便民服务和信息查询",
      icon: Users,
      features: ["信息查询", "在线服务", "便民应用"]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10">
        {/* 主要内容区域 */}
        <main className="max-w-7xl mx-auto px-6 py-20">
          {/* 英雄区域 */}
          <section className="text-center mb-20 animate-fade-in">
            <div className="inline-flex items-center space-x-2 bg-blue-50/50 backdrop-blur-sm px-6 py-3 rounded-full mb-8">
              <Sparkles className="w-5 h-5 text-blue-600" />
              <span className="text-lg font-medium text-blue-700">智慧政务 · 数据驱动 · 创新未来</span>
            </div>
            
            <h1 className="text-6xl md:text-7xl font-bold mb-8">
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">云宇政数平台</span>
            </h1>
            
            <p className="text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              统一的政务数据管理与服务平台，为政府数字化转型提供全方位的数据支撑，
              实现跨部门数据共享、智能分析决策和高效政务服务
            </p>
            
            <div className="flex items-center justify-center space-x-8 mb-12 text-lg text-gray-500">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-6 h-6 text-green-500" />
                <span>24个委办局接入</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-6 h-6 text-green-500" />
                <span>2.4TB数据存储</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-6 h-6 text-green-500" />
                <span>99.9%系统可用性</span>
              </div>
            </div>
          </section>

          {/* 核心能力 / 业务模块 */}
          <section className="mb-20 animate-slide-up">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                {showModuleOverlay ? '业务模块快速入口' : '平台核心能力'}
              </h2>
              <p className="text-xl text-gray-600">
                {showModuleOverlay ? '选择您需要的功能模块，直接进入对应的业务操作界面' : '为政务数字化转型提供全方位的技术支撑'}
              </p>
            </div>

            {!showModuleOverlay ? (
              // 核心能力和业务模块整合内容
              <div className="space-y-12">
                {/* 业务模块区域 */}
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">业务模块</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* 资源池管理 */}
                    <Link href="/resources">
                      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20 cursor-pointer h-64">
                        <div className="text-center h-full flex flex-col justify-center">
                          <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                            <HardDrive className="w-8 h-8 text-white" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-900 mb-4">资源池管理</h3>
                          <p className="text-gray-600 leading-relaxed">数据资源统一管理，资源目录和权限控制</p>
                        </div>
                      </div>
                    </Link>

                    {/* 数据大屏 */}
                    <Link href="/screen">
                      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20 cursor-pointer h-64">
                        <div className="text-center h-full flex flex-col justify-center">
                          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                            <Monitor className="w-8 h-8 text-white" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-900 mb-4">数据大屏</h3>
                          <p className="text-gray-600 leading-relaxed">实时数据可视化展示，支持多种图表和指标监控</p>
                        </div>
                      </div>
                    </Link>

                    {/* 报表分析 */}
                    <Link href="/reports">
                      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20 cursor-pointer h-64">
                        <div className="text-center h-full flex flex-col justify-center">
                          <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                            <FileText className="w-8 h-8 text-white" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-900 mb-4">报表分析</h3>
                          <p className="text-gray-600 leading-relaxed">多维度数据分析报表，支持自定义报表生成</p>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>
                
                {/* 核心能力区域 */}
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">核心能力</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                {capabilities.map((capability, index) => {
                  const Icon = capability.icon
                  const isLifecycleCard = capability.title === "数据全生命周期管理"
                  const isSecurityCard = capability.title === "数据安全治理"
                  const isSharingCard = capability.title === "跨部门数据共享"
                  const isAnalyticsCard = capability.title === "智能数据分析"
                  const isOpsCard = capability.title === "智能运维监控"

                  const handleCardClick = () => {
                    if (isSecurityCard) {
                      window.location.href = '/security-governance'
                    } else if (isSharingCard) {
                      window.location.href = '/data-sharing'
                    } else if (isAnalyticsCard) {
                      window.location.href = '/intelligent-analytics'
                    }
                  }

                  return (
                    <div key={capability.title} className="relative">
                      <div
                        className={`bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20 h-64 ${(isSecurityCard || isSharingCard) ? 'cursor-pointer' : ''}`}
                        style={{ animationDelay: `${index * 100}ms` }}
                        onMouseEnter={() => {
                          if (isLifecycleCard) {
                            setShowLifecycleModules(true)
                          } else if (isOpsCard) {
                            setShowOpsModules(true)
                          }
                        }}
                        onMouseLeave={() => {
                          if (isLifecycleCard) {
                            setShowLifecycleModules(false)
                          } else if (isOpsCard) {
                            setShowOpsModules(false)
                          }
                        }}
                        onClick={handleCardClick}
                      >
                        <div className="text-center h-full flex flex-col justify-center">
                          <div className={`w-16 h-16 bg-gradient-to-br ${capability.gradient} rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110`}>
                            <Icon className="w-8 h-8 text-white" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-900 mb-4">{capability.title}</h3>
                          <p className="text-gray-600 leading-relaxed">{capability.description}</p>
                        </div>
                      </div>

                      {/* 数据全生命周期管理卡片的悬浮模块 showLifecycleModules */}
                      {isLifecycleCard && showLifecycleModules && (
                        <div
                          className="absolute left-full top-0 ml-4 z-50 animate-fade-in"
                          onMouseEnter={() => setShowLifecycleModules(true)}
                          onMouseLeave={() => setShowLifecycleModules(false)}
                        >
                          <div className="grid grid-cols-1 gap-4 w-80">
                            {/* 数据采集卡片 */}
                            <Link href="/collection">
                              <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50">
                                <div className="flex items-center space-x-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <Database className="w-6 h-6 text-white" />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-lg font-bold text-gray-900 group-hover:text-purple-700 transition-colors">数据采集</h4>
                                    <p className="text-sm text-gray-600">多源数据采集接入</p>
                                  </div>
                                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-purple-600 transform group-hover:translate-x-1 transition-all" />
                                </div>
                              </div>
                            </Link>

                            {/* 数据汇聚卡片 */}
                            <Link href="/aggregation">
                              <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50">
                                <div className="flex items-center space-x-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <GitMerge className="w-6 h-6 text-white" />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-lg font-bold text-gray-900 group-hover:text-orange-700 transition-colors">数据汇聚</h4>
                                    <p className="text-sm text-gray-600">跨系统数据整合</p>
                                  </div>
                                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-orange-600 transform group-hover:translate-x-1 transition-all" />
                                </div>
                              </div>
                            </Link>

                            {/* 数据治理卡片 */}
                            <Link href="/governance">
                              <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50">
                                <div className="flex items-center space-x-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-rose-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <Shield className="w-6 h-6 text-white" />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-lg font-bold text-gray-900 group-hover:text-red-700 transition-colors">数据治理</h4>
                                    <p className="text-sm text-gray-600">数据质量管控</p>
                                  </div>
                                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-red-600 transform group-hover:translate-x-1 transition-all" />
                                </div>
                              </div>
                            </Link>
                          </div>
                        </div>
                      )}

                      {/* 智能运维监控卡片的悬浮模块 */}
                      {isOpsCard && showOpsModules && (
                        <div
                          className="absolute right-full top-0 mr-4 z-50 animate-fade-in"
                          onMouseEnter={() => setShowOpsModules(true)}
                          onMouseLeave={() => setShowOpsModules(false)}
                        >
                          <div className="grid grid-cols-1 gap-4 w-80">
                            {/* 设备监控卡片 */}
                            <Link href="/monitoring">
                              <div className="bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50">
                                <div className="flex items-center space-x-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <Server className="w-6 h-6 text-white" />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-lg font-bold text-gray-900 group-hover:text-teal-700 transition-colors">设备监控</h4>
                                    <p className="text-sm text-gray-600">服务器设备实时监控</p>
                                  </div>
                                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-teal-600 transform group-hover:translate-x-1 transition-all" />
                                </div>
                              </div>
                            </Link>

                            {/* 网络管理卡片 */}
                            <Link href="/network">
                              <div className="bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50">
                                <div className="flex items-center space-x-4">
                                  <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center shadow-lg">
                                    <Network className="w-6 h-6 text-white" />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="text-lg font-bold text-gray-900 group-hover:text-pink-700 transition-colors">网络管理</h4>
                                    <p className="text-sm text-gray-600">网络拓扑和流量监控</p>
                                  </div>
                                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-pink-600 transform group-hover:translate-x-1 transition-all" />
                                </div>
                              </div>
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>
                  )
                })}
                  </div>
                </div>

                {/* 进入平台按钮 */}
                <div className="text-center">
                  <Link
                    href="/dashboard"
                    className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <Zap className="w-5 h-5" />
                    <span className="font-semibold">进入主控台</span>
                  </Link>
                </div>
              </div>
            ) : (
              // 业务模块内容
              <div
                className="animate-fade-in"
                onMouseEnter={() => setShowModuleOverlay(true)}
                onMouseLeave={() => setShowModuleOverlay(false)}
              >

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {businessModules.map((module, index) => {
                    const Icon = module.icon
                    return (
                      <Link key={module.name} href={module.href}>
                        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer border border-white/20">
                          <div className="text-center">
                            <div className={`w-16 h-16 bg-gradient-to-br ${module.gradient} rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110`}>
                              <Icon className="w-8 h-8 text-white" />
                            </div>
                            <div className="flex items-center justify-center space-x-2 mb-2">
                              <span className="text-2xl">{module.logo}</span>
                              <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-700 transition-colors">
                                {module.name}
                              </h3>
                            </div>
                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                              {module.description}
                            </p>
                            <div className="flex flex-wrap justify-center gap-1 mb-3">
                              {module.features.slice(0, 3).map((feature, idx) => (
                                <span key={idx} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                  {feature}
                                </span>
                              ))}
                            </div>
                            <div className="flex items-center justify-center text-blue-600 group-hover:text-blue-700 transition-colors">
                              <span className="text-sm font-medium">进入系统</span>
                              <ArrowRight className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" />
                            </div>
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>

                <div className="mt-8 text-center">
                  <Link
                    href="/dashboard"
                    className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <Zap className="w-5 h-5" />
                    <span className="font-semibold">进入主控台</span>
                  </Link>
                </div>
              </div>
            )}
          </section>

          {/* 解决的场景 */}
          <section className="mb-20 animate-slide-up" style={{ animationDelay: '200ms' }}>
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">解决的应用场景</h2>
              <p className="text-xl text-gray-600">覆盖政务管理的各个关键环节，提升政府治理效能</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {scenarios.map((scenario, index) => {
                const Icon = scenario.icon
                return (
                  <div
                    key={scenario.title}
                    className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex items-start space-x-6">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{scenario.title}</h3>
                        <p className="text-gray-600 leading-relaxed">{scenario.description}</p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </section>

          {/* 数据来源单位 */}
          <section className="mb-20 animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">数据来源单位</h2>
              <p className="text-xl text-gray-600">覆盖全市24个主要委办局，构建统一的数据生态体系</p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-white/20">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                {dataSourceUnits.map((unit, index) => (
                  <div
                    key={unit}
                    className="text-center p-4 rounded-xl bg-gray-50/50 hover:bg-blue-50/50 transition-all duration-300 group"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <Building2 className="w-8 h-8 text-gray-400 group-hover:text-blue-500 mx-auto mb-3 transition-colors" />
                    <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors">{unit}</span>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* 权威数据覆盖 */}
          <section className="mb-20 animate-slide-up" style={{ animationDelay: '600ms' }}>
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">权威数据覆盖</h2>
              <p className="text-xl text-gray-600">全面覆盖政务核心数据领域，确保数据权威性和完整性</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {authorityData.map((data, index) => (
                <div
                  key={data.area}
                  className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">{data.area}</h3>
                    <span className="text-3xl font-bold text-blue-600">{data.coverage}</span>
                  </div>
                  <p className="text-gray-600 mb-6">{data.source}</p>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-1000"
                      style={{ width: data.coverage }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* 目标客户 */}
          <section className="mb-20 animate-slide-up" style={{ animationDelay: '800ms' }}>
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">服务对象</h2>
              <p className="text-xl text-gray-600">为不同类型用户提供专业化的数据服务和解决方案</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {targetUsers.map((user, index) => {
                const Icon = user.icon
                return (
                  <div
                    key={user.type}
                    className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group text-center border border-white/20"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                      <Icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">{user.type}</h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">{user.desc}</p>
                    <div className="space-y-2">
                      {user.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center justify-center text-sm text-gray-500">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>
          </section>



          {/* 底部行动号召 */}
          <section className="text-center animate-slide-up" style={{ animationDelay: '1200ms' }}>
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white shadow-2xl">
              <h2 className="text-4xl font-bold mb-6">准备好开始了吗？</h2>
              <p className="text-2xl mb-12 text-blue-100">
                立即体验云宇政数平台，开启您的智慧政务数据管理之旅
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8">
                <Link
                  href="/dashboard"
                  className="bg-white text-blue-600 hover:bg-blue-50 font-bold py-5 px-12 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center text-xl"
                >
                  <Zap className="w-6 h-6 mr-3" />
                  进入平台
                </Link>
                <button className="bg-blue-500/20 backdrop-blur-sm text-white hover:bg-blue-500/30 font-medium py-5 px-12 rounded-2xl transition-all duration-300 border border-white/20 flex items-center text-xl">
                  <FileText className="w-6 h-6 mr-3" />
                  了解更多
                </button>
              </div>
            </div>
          </section>
        </main>

        {/* 页脚 */}
        <footer className="bg-white/80 backdrop-blur-xl border-t border-white/20 mt-20">
          <div className="max-w-7xl mx-auto px-6 py-12">
            <div className="text-center text-gray-600">
              <p className="text-lg mb-3">© 2024 云宇政数平台. 保留所有权利.</p>
              <p className="text-base">为政府数字化转型提供专业的数据管理服务</p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  )
}
