"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/intelligent-analytics/page",{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js":
/*!************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js ***!
  \************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileText; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst FileText = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"FileText\", [\n    [\n        \"path\",\n        {\n            d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\",\n            key: \"1nnpy2\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"14 2 14 8 20 8\",\n            key: \"1ew0cm\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"8\",\n            y1: \"13\",\n            y2: \"13\",\n            key: \"14keom\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"8\",\n            y1: \"17\",\n            y2: \"17\",\n            key: \"17nazh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"10\",\n            x2: \"8\",\n            y1: \"9\",\n            y2: \"9\",\n            key: \"1a5vjj\"\n        }\n    ]\n]);\n //# sourceMappingURL=file-text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/intelligent-analytics/page.tsx":
/*!************************************************!*\
  !*** ./src/app/intelligent-analytics/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ IntelligentAnalyticsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowLeft,BarChart3,Brain,Database,Download,Eye,FileText,LineChart,Monitor,PieChart,RefreshCw,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction IntelligentAnalyticsPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [selectedTimeRange, setSelectedTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"7d\");\n    // 分析概览数据\n    const analyticsOverview = {\n        totalDataSources: 24,\n        activeAnalytics: 156,\n        realTimeMetrics: 89,\n        predictiveModels: 12,\n        dataVolume: \"2.4TB\",\n        processingSpeed: \"1.2M/s\"\n    };\n    // 多维度分析数据\n    const dimensionAnalysis = [\n        {\n            id: 1,\n            name: \"人口分布分析\",\n            dimensions: [\n                \"年龄\",\n                \"性别\",\n                \"地区\",\n                \"职业\"\n            ],\n            dataSource: \"人口基础信息库\",\n            lastUpdate: \"2024-01-15 14:30\",\n            status: \"active\",\n            insights: 156,\n            accuracy: 94.2\n        },\n        {\n            id: 2,\n            name: \"经济指标分析\",\n            dimensions: [\n                \"GDP\",\n                \"产业结构\",\n                \"就业率\",\n                \"投资额\"\n            ],\n            dataSource: \"经济统计数据库\",\n            lastUpdate: \"2024-01-15 12:00\",\n            status: \"active\",\n            insights: 89,\n            accuracy: 91.8\n        },\n        {\n            id: 3,\n            name: \"城市发展分析\",\n            dimensions: [\n                \"基础设施\",\n                \"环境质量\",\n                \"公共服务\",\n                \"交通状况\"\n            ],\n            dataSource: \"城市管理数据库\",\n            lastUpdate: \"2024-01-15 10:15\",\n            status: \"processing\",\n            insights: 67,\n            accuracy: 88.5\n        }\n    ];\n    // 实时监控指标\n    const realTimeMetrics = [\n        {\n            id: 1,\n            name: \"数据处理速度\",\n            value: \"1.2M\",\n            unit: \"条/秒\",\n            trend: \"up\",\n            change: \"+12.5%\",\n            status: \"normal\"\n        },\n        {\n            id: 2,\n            name: \"系统响应时间\",\n            value: \"156\",\n            unit: \"ms\",\n            trend: \"down\",\n            change: \"-8.3%\",\n            status: \"good\"\n        },\n        {\n            id: 3,\n            name: \"数据准确率\",\n            value: \"99.2\",\n            unit: \"%\",\n            trend: \"up\",\n            change: \"+0.5%\",\n            status: \"excellent\"\n        },\n        {\n            id: 4,\n            name: \"并发用户数\",\n            value: \"2,847\",\n            unit: \"人\",\n            trend: \"up\",\n            change: \"+15.2%\",\n            status: \"normal\"\n        }\n    ];\n    // 预测分析模型\n    const predictiveModels = [\n        {\n            id: 1,\n            name: \"人口增长预测模型\",\n            type: \"时间序列预测\",\n            accuracy: 92.5,\n            lastTrained: \"2024-01-10\",\n            status: \"active\",\n            predictions: 156,\n            confidence: \"high\"\n        },\n        {\n            id: 2,\n            name: \"经济发展趋势预测\",\n            type: \"回归分析\",\n            accuracy: 89.3,\n            lastTrained: \"2024-01-08\",\n            status: \"active\",\n            predictions: 89,\n            confidence: \"medium\"\n        },\n        {\n            id: 3,\n            name: \"交通流量预测模型\",\n            type: \"机器学习\",\n            accuracy: 95.1,\n            lastTrained: \"2024-01-12\",\n            status: \"training\",\n            predictions: 234,\n            confidence: \"high\"\n        }\n    ];\n    // 可视化图表配置\n    const visualizations = [\n        {\n            id: 1,\n            name: \"人口分布热力图\",\n            type: \"heatmap\",\n            category: \"地理分析\",\n            updateFreq: \"实时\",\n            viewers: 1247\n        },\n        {\n            id: 2,\n            name: \"经济指标趋势图\",\n            type: \"line\",\n            category: \"经济分析\",\n            updateFreq: \"每日\",\n            viewers: 856\n        },\n        {\n            id: 3,\n            name: \"部门协作网络图\",\n            type: \"network\",\n            category: \"组织分析\",\n            updateFreq: \"每周\",\n            viewers: 423\n        },\n        {\n            id: 4,\n            name: \"资源使用饼图\",\n            type: \"pie\",\n            category: \"资源分析\",\n            updateFreq: \"每小时\",\n            viewers: 678\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n            case \"normal\":\n            case \"good\":\n                return \"text-green-600 bg-green-100\";\n            case \"processing\":\n            case \"training\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"excellent\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"error\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getTrendIcon = (trend)=>{\n        return trend === \"up\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4 text-green-500\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4 text-red-500 transform rotate-180\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    };\n    const getConfidenceColor = (confidence)=>{\n        switch(confidence){\n            case \"high\":\n                return \"text-green-600 bg-green-100\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"low\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-green-400/20 to-emerald-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-emerald-400/20 to-green-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/\",\n                                                className: \"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"返回首页\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-px h-6 bg-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: \"智能数据分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Intelligent Data Analytics\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedTimeRange,\n                                                onChange: (e)=>setSelectedTimeRange(e.target.value),\n                                                className: \"px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"1d\",\n                                                        children: \"最近1天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"7d\",\n                                                        children: \"最近7天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"30d\",\n                                                        children: \"最近30天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"90d\",\n                                                        children: \"最近90天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"刷新数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-2 bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"导出报告\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-6 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"-mb-px flex space-x-8\",\n                                        children: [\n                                            {\n                                                id: \"overview\",\n                                                name: \"分析概览\",\n                                                icon: _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                            },\n                                            {\n                                                id: \"dimensions\",\n                                                name: \"多维分析\",\n                                                icon: _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                            },\n                                            {\n                                                id: \"realtime\",\n                                                name: \"实时监控\",\n                                                icon: _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                            },\n                                            {\n                                                id: \"predictions\",\n                                                name: \"预测分析\",\n                                                icon: _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                            },\n                                            {\n                                                id: \"visualizations\",\n                                                name: \"可视化\",\n                                                icon: _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                            },\n                                            {\n                                                id: \"reports\",\n                                                name: \"报表中心\",\n                                                icon: _barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                            }\n                                        ].map((tab)=>{\n                                            const Icon = tab.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? \"border-green-500 text-green-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"数据源总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                                    children: analyticsOverview.totalDataSources\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"活跃分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-green-600\",\n                                                                    children: analyticsOverview.activeAnalytics\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"实时指标\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-blue-600\",\n                                                                    children: analyticsOverview.realTimeMetrics\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"预测模型\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-purple-600\",\n                                                                    children: analyticsOverview.predictiveModels\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"数据量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-orange-600\",\n                                                                    children: analyticsOverview.dataVolume\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"处理速度\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-teal-600\",\n                                                                    children: analyticsOverview.processingSpeed\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-gray-900 mb-4\",\n                                                        children: \"数据处理趋势\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"数据处理趋势图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-gray-900 mb-4\",\n                                                        children: \"分析准确率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"准确率统计图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"dimensions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"多维度数据分析\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                                    children: \"创建新分析\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: dimensionAnalysis.map((analysis)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: analysis.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(analysis.status)),\n                                                                                children: analysis.status === \"active\" ? \"运行中\" : \"处理中\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                                                        children: \"分析维度\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 436,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: analysis.dimensions.map((dim, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\",\n                                                                                                children: dim\n                                                                                            }, idx, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                                lineNumber: 439,\n                                                                                                columnNumber: 35\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 437,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"数据源\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 446,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: analysis.dataSource\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 447,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"最后更新\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 450,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: analysis.lastUpdate\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 451,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                lineNumber: 449,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"洞察数量\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 457,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-2xl font-bold text-green-600\",\n                                                                                        children: analysis.insights\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 458,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"准确率\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 461,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                                                        children: [\n                                                                                            analysis.accuracy,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 462,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                        children: \"查看详情\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-gray-600 hover:text-gray-800 text-sm\",\n                                                                        children: \"配置\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, analysis.id, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"realtime\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-900 mb-6\",\n                                                children: \"实时监控指标\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                children: realTimeMetrics.map((metric)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border border-gray-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900\",\n                                                                        children: metric.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(metric.status)),\n                                                                        children: metric.status === \"excellent\" ? \"优秀\" : metric.status === \"good\" ? \"良好\" : \"正常\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-end space-x-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                                        children: metric.value\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600 pb-1\",\n                                                                        children: metric.unit\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    getTrendIcon(metric.trend),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(metric.trend === \"up\" ? \"text-green-600\" : \"text-red-600\"),\n                                                                        children: metric.change\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, metric.id, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-gray-900 mb-4\",\n                                                        children: \"实时数据流\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"实时数据流图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-gray-900 mb-4\",\n                                                        children: \"系统性能监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"系统性能图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"predictions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"预测分析模型\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                                    children: \"训练新模型\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: predictiveModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                    children: model.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(model.status)),\n                                                                    children: model.status === \"active\" ? \"运行中\" : \"训练中\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"模型类型\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 563,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: model.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 564,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"最后训练\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 567,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: model.lastTrained\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 566,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                                            children: \"模型准确率\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1 bg-gray-200 rounded-full h-3\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-3 rounded-full \".concat(model.accuracy >= 90 ? \"bg-green-500\" : model.accuracy >= 80 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                        style: {\n                                                                                            width: \"\".concat(model.accuracy, \"%\")\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                        lineNumber: 576,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-lg font-bold text-blue-600\",\n                                                                                    children: [\n                                                                                        model.accuracy,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"预测数量\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 590,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xl font-bold text-purple-600\",\n                                                                                    children: model.predictions\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 591,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"置信度\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getConfidenceColor(model.confidence)),\n                                                                                    children: model.confidence === \"high\" ? \"高\" : model.confidence === \"medium\" ? \"中\" : \"低\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                    children: \"查看预测\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-600 hover:text-gray-800 text-sm\",\n                                                                    children: \"重新训练\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, model.id, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"visualizations\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"数据可视化图表\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                                                    children: \"创建图表\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: visualizations.map((viz)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: viz.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium\",\n                                                                    children: viz.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"分类\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 636,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: viz.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"更新频率\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: viz.updateFreq\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 641,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"浏览次数\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xl font-bold text-green-600\",\n                                                                            children: viz.viewers.toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                            lineNumber: 645,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-32 bg-gray-100 rounded-lg flex items-center justify-center mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowLeft_BarChart3_Brain_Database_Download_Eye_FileText_LineChart_Monitor_PieChart_RefreshCw_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-8 h-8 mx-auto mb-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"图表预览\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                        lineNumber: 652,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                    children: \"查看\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-600 hover:text-gray-800 text-sm\",\n                                                                    children: \"编辑\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-600 hover:text-gray-800 text-sm\",\n                                                                    children: \"分享\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, viz.id, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\intelligent-analytics\\\\page.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(IntelligentAnalyticsPage, \"kfisFSgc6Dej3K4KhYxj9U0WGAQ=\");\n_c = IntelligentAnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"IntelligentAnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/intelligent-analytics/page.tsx\n"));

/***/ })

});