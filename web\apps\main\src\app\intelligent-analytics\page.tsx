'use client'

import { useState } from 'react'
import Link from 'next/link'
import ReportsSystemPage from '@/app/reports/page'

import {
  BarChart3,
  <PERSON><PERSON>dingUp,
  <PERSON><PERSON>hart,
  LineChart,
  Activity,
  Eye,
  Brain,
  Zap,
  ArrowLeft,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Search,
  Calendar,
  Clock,
  Target,
  AlertCircle,
  CheckCircle,
  Database,
  Users,
  Building2,
  Globe,
  Cpu,
  Monitor,
  FileText,
  Share2
} from 'lucide-react'

export default function IntelligentAnalyticsPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d')

  // 分析概览数据
  const analyticsOverview = {
    totalDataSources: 24,
    activeAnalytics: 156,
    realTimeMetrics: 89,
    predictiveModels: 12,
    dataVolume: '2.4TB',
    processingSpeed: '1.2M/s'
  }

  // 多维度分析数据
  const dimensionAnalysis = [
    {
      id: 1,
      name: '人口分布分析',
      dimensions: ['年龄', '性别', '地区', '职业'],
      dataSource: '人口基础信息库',
      lastUpdate: '2024-01-15 14:30',
      status: 'active',
      insights: 156,
      accuracy: 94.2
    },
    {
      id: 2,
      name: '经济指标分析',
      dimensions: ['GDP', '产业结构', '就业率', '投资额'],
      dataSource: '经济统计数据库',
      lastUpdate: '2024-01-15 12:00',
      status: 'active',
      insights: 89,
      accuracy: 91.8
    },
    {
      id: 3,
      name: '城市发展分析',
      dimensions: ['基础设施', '环境质量', '公共服务', '交通状况'],
      dataSource: '城市管理数据库',
      lastUpdate: '2024-01-15 10:15',
      status: 'processing',
      insights: 67,
      accuracy: 88.5
    }
  ]

  // 实时监控指标
  const realTimeMetrics = [
    {
      id: 1,
      name: '数据处理速度',
      value: '1.2M',
      unit: '条/秒',
      trend: 'up',
      change: '+12.5%',
      status: 'normal'
    },
    {
      id: 2,
      name: '系统响应时间',
      value: '156',
      unit: 'ms',
      trend: 'down',
      change: '-8.3%',
      status: 'good'
    },
    {
      id: 3,
      name: '数据准确率',
      value: '99.2',
      unit: '%',
      trend: 'up',
      change: '+0.5%',
      status: 'excellent'
    },
    {
      id: 4,
      name: '并发用户数',
      value: '2,847',
      unit: '人',
      trend: 'up',
      change: '+15.2%',
      status: 'normal'
    }
  ]

  // 预测分析模型
  const predictiveModels = [
    {
      id: 1,
      name: '人口增长预测模型',
      type: '时间序列预测',
      accuracy: 92.5,
      lastTrained: '2024-01-10',
      status: 'active',
      predictions: 156,
      confidence: 'high'
    },
    {
      id: 2,
      name: '经济发展趋势预测',
      type: '回归分析',
      accuracy: 89.3,
      lastTrained: '2024-01-08',
      status: 'active',
      predictions: 89,
      confidence: 'medium'
    },
    {
      id: 3,
      name: '交通流量预测模型',
      type: '机器学习',
      accuracy: 95.1,
      lastTrained: '2024-01-12',
      status: 'training',
      predictions: 234,
      confidence: 'high'
    }
  ]

  // 可视化图表配置
  const visualizations = [
    {
      id: 1,
      name: '人口分布热力图',
      type: 'heatmap',
      category: '地理分析',
      updateFreq: '实时',
      viewers: 1247
    },
    {
      id: 2,
      name: '经济指标趋势图',
      type: 'line',
      category: '经济分析',
      updateFreq: '每日',
      viewers: 856
    },
    {
      id: 3,
      name: '部门协作网络图',
      type: 'network',
      category: '组织分析',
      updateFreq: '每周',
      viewers: 423
    },
    {
      id: 4,
      name: '资源使用饼图',
      type: 'pie',
      category: '资源分析',
      updateFreq: '每小时',
      viewers: 678
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'normal': case 'good': return 'text-green-600 bg-green-100'
      case 'processing': case 'training': return 'text-yellow-600 bg-yellow-100'
      case 'excellent': return 'text-blue-600 bg-blue-100'
      case 'error': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? (
      <TrendingUp className="w-4 h-4 text-green-500" />
    ) : (
      <TrendingUp className="w-4 h-4 text-red-500 transform rotate-180" />
    )
  }

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }
  

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-green-400/20 to-emerald-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-emerald-400/20 to-green-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10">
        {/* 头部导航 */}
        <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                  <ArrowLeft className="w-5 h-5" />
                  <span>返回首页</span>
                </Link>
                <div className="w-px h-6 bg-gray-300"></div>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">智能数据分析</h1>
                    <p className="text-sm text-gray-600">Intelligent Data Analytics</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <select 
                  value={selectedTimeRange}
                  onChange={(e) => setSelectedTimeRange(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="1d">最近1天</option>
                  <option value="7d">最近7天</option>
                  <option value="30d">最近30天</option>
                  <option value="90d">最近90天</option>
                </select>
                <button className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                  <RefreshCw className="w-4 h-4" />
                  <span>刷新数据</span>
                </button>
                <button className="flex items-center space-x-2 bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <Download className="w-4 h-4" />
                  <span>导出报告</span>
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* 主要内容 */}
        <main className="max-w-7xl mx-auto px-6 py-8">
          {/* 标签页导航 */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'overview', name: '分析概览', icon: BarChart3 },
                  { id: 'dimensions', name: '多维分析', icon: PieChart },
                  { id: 'realtime', name: '实时监控', icon: Activity },
                  { id: 'predictions', name: '预测分析', icon: Brain },
                  { id: 'visualizations', name: '可视化', icon: Eye },
                  { id: 'reports', name: '报表中心', icon: FileText }
                ].map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-green-500 text-green-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{tab.name}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* 分析概览 */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* 关键指标卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">数据源总数</p>
                      <p className="text-3xl font-bold text-gray-900">{analyticsOverview.totalDataSources}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                      <Database className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">活跃分析</p>
                      <p className="text-3xl font-bold text-green-600">{analyticsOverview.activeAnalytics}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                      <BarChart3 className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">实时指标</p>
                      <p className="text-3xl font-bold text-blue-600">{analyticsOverview.realTimeMetrics}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center">
                      <Activity className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">预测模型</p>
                      <p className="text-3xl font-bold text-purple-600">{analyticsOverview.predictiveModels}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <Brain className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">数据量</p>
                      <p className="text-3xl font-bold text-orange-600">{analyticsOverview.dataVolume}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                      <Database className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">处理速度</p>
                      <p className="text-3xl font-bold text-teal-600">{analyticsOverview.processingSpeed}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center">
                      <Zap className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 分析趋势图表 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">数据处理趋势</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <LineChart className="w-12 h-12 mx-auto mb-2" />
                      <p>数据处理趋势图表</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">分析准确率</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <Target className="w-12 h-12 mx-auto mb-2" />
                      <p>准确率统计图表</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 多维分析 */}
          {activeTab === 'dimensions' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">多维度数据分析</h3>
                  <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    创建新分析
                  </button>
                </div>

                <div className="space-y-6">
                  {dimensionAnalysis.map((analysis) => (
                    <div key={analysis.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <h4 className="text-lg font-semibold text-gray-900">{analysis.name}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(analysis.status)}`}>
                              {analysis.status === 'active' ? '运行中' : '处理中'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                              <p className="text-sm text-gray-600 mb-1">分析维度</p>
                              <div className="flex flex-wrap gap-1">
                                {analysis.dimensions.map((dim, idx) => (
                                  <span key={idx} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                    {dim}
                                  </span>
                                ))}
                              </div>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">数据源</p>
                              <p className="font-medium text-gray-900">{analysis.dataSource}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">最后更新</p>
                              <p className="font-medium text-gray-900">{analysis.lastUpdate}</p>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">洞察数量</p>
                              <p className="text-2xl font-bold text-green-600">{analysis.insights}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">准确率</p>
                              <p className="text-2xl font-bold text-blue-600">{analysis.accuracy}%</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          <button className="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                          <button className="text-gray-600 hover:text-gray-800 text-sm">配置</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 实时监控 */}
          {activeTab === 'realtime' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <h3 className="text-lg font-bold text-gray-900 mb-6">实时监控指标</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {realTimeMetrics.map((metric) => (
                    <div key={metric.id} className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border border-gray-200">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-gray-900">{metric.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(metric.status)}`}>
                          {metric.status === 'excellent' ? '优秀' :
                           metric.status === 'good' ? '良好' : '正常'}
                        </span>
                      </div>

                      <div className="flex items-end space-x-2 mb-3">
                        <span className="text-3xl font-bold text-gray-900">{metric.value}</span>
                        <span className="text-sm text-gray-600 pb-1">{metric.unit}</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        {getTrendIcon(metric.trend)}
                        <span className={`text-sm font-medium ${
                          metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {metric.change}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 实时图表 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">实时数据流</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <Activity className="w-12 h-12 mx-auto mb-2" />
                      <p>实时数据流图表</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">系统性能监控</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <Monitor className="w-12 h-12 mx-auto mb-2" />
                      <p>系统性能图表</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 预测分析 */}
          {activeTab === 'predictions' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">预测分析模型</h3>
                  <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    训练新模型
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {predictiveModels.map((model) => (
                    <div key={model.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900">{model.name}</h4>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(model.status)}`}>
                          {model.status === 'active' ? '运行中' : '训练中'}
                        </span>
                      </div>

                      <div className="space-y-3 mb-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">模型类型</p>
                            <p className="font-medium text-gray-900">{model.type}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">最后训练</p>
                            <p className="font-medium text-gray-900">{model.lastTrained}</p>
                          </div>
                        </div>

                        <div>
                          <p className="text-sm text-gray-600 mb-2">模型准确率</p>
                          <div className="flex items-center space-x-3">
                            <div className="flex-1 bg-gray-200 rounded-full h-3">
                              <div
                                className={`h-3 rounded-full ${
                                  model.accuracy >= 90 ? 'bg-green-500' :
                                  model.accuracy >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${model.accuracy}%` }}
                              ></div>
                            </div>
                            <span className="text-lg font-bold text-blue-600">{model.accuracy}%</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">预测数量</p>
                            <p className="text-xl font-bold text-purple-600">{model.predictions}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">置信度</p>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(model.confidence)}`}>
                              {model.confidence === 'high' ? '高' : model.confidence === 'medium' ? '中' : '低'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-end space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm">查看预测</button>
                        <button className="text-gray-600 hover:text-gray-800 text-sm">重新训练</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 可视化 */}
          {activeTab === 'visualizations' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">数据可视化图表</h3>
                  <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    创建图表
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {visualizations.map((viz) => (
                    <div key={viz.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-gray-900">{viz.name}</h4>
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
                          {viz.type}
                        </span>
                      </div>

                      <div className="space-y-3 mb-4">
                        <div>
                          <p className="text-sm text-gray-600">分类</p>
                          <p className="font-medium text-gray-900">{viz.category}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">更新频率</p>
                          <p className="font-medium text-gray-900">{viz.updateFreq}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">浏览次数</p>
                          <p className="text-xl font-bold text-green-600">{viz.viewers.toLocaleString()}</p>
                        </div>
                      </div>

                      <div className="h-32 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                        <div className="text-center text-gray-500">
                          <Eye className="w-8 h-8 mx-auto mb-1" />
                          <p className="text-sm">图表预览</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-end space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm">查看</button>
                        <button className="text-gray-600 hover:text-gray-800 text-sm">编辑</button>
                        <button className="text-gray-600 hover:text-gray-800 text-sm">分享</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 报表中心 */}
          {activeTab === 'reports' && <ReportsSystemPage />}
        </main>
      </div>
    </div>
  )
}
