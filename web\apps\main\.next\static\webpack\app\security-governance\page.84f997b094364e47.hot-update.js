"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/security-governance/page",{

/***/ "(app-pages-browser)/./src/app/security-governance/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/security-governance/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SecurityGovernancePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowLeft,BarChart3,CheckCircle,Clock,Database,Download,FileCheck,FileText,Filter,Key,RefreshCw,Search,Settings,Shield,TrendingUp,UserCheck,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SecurityGovernancePage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 安全概览数据\n    const securityOverview = {\n        totalAssets: 1247,\n        secureAssets: 1189,\n        riskAssets: 58,\n        complianceRate: 95.3,\n        lastScan: \"2024-01-15 14:30:00\"\n    };\n    // 权限管理数据\n    const permissionData = [\n        {\n            id: 1,\n            user: \"张三\",\n            department: \"市政府办公厅\",\n            role: \"数据管理员\",\n            permissions: [\n                \"数据查看\",\n                \"数据导出\",\n                \"权限管理\"\n            ],\n            status: \"active\",\n            lastAccess: \"2024-01-15 10:30\"\n        },\n        {\n            id: 2,\n            user: \"李四\",\n            department: \"发展改革委\",\n            role: \"业务用户\",\n            permissions: [\n                \"数据查看\"\n            ],\n            status: \"active\",\n            lastAccess: \"2024-01-15 09:15\"\n        },\n        {\n            id: 3,\n            user: \"王五\",\n            department: \"教育局\",\n            role: \"数据分析师\",\n            permissions: [\n                \"数据查看\",\n                \"数据分析\"\n            ],\n            status: \"pending\",\n            lastAccess: \"2024-01-14 16:45\"\n        }\n    ];\n    // 合规检查数据\n    const complianceChecks = [\n        {\n            id: 1,\n            name: \"个人信息保护检查\",\n            status: \"passed\",\n            score: 98,\n            lastCheck: \"2024-01-15 08:00\",\n            issues: 2\n        },\n        {\n            id: 2,\n            name: \"数据访问权限审计\",\n            status: \"warning\",\n            score: 85,\n            lastCheck: \"2024-01-15 06:00\",\n            issues: 8\n        },\n        {\n            id: 3,\n            name: \"数据传输加密检查\",\n            status: \"passed\",\n            score: 100,\n            lastCheck: \"2024-01-15 04:00\",\n            issues: 0\n        },\n        {\n            id: 4,\n            name: \"数据备份完整性检查\",\n            status: \"failed\",\n            score: 65,\n            lastCheck: \"2024-01-15 02:00\",\n            issues: 15\n        }\n    ];\n    // 安全事件数据\n    const securityEvents = [\n        {\n            id: 1,\n            type: \"warning\",\n            title: \"异常数据访问\",\n            description: \"检测到用户在非工作时间大量访问敏感数据\",\n            time: \"2024-01-15 23:45\",\n            severity: \"medium\",\n            status: \"investigating\"\n        },\n        {\n            id: 2,\n            type: \"error\",\n            title: \"权限越权尝试\",\n            description: \"用户尝试访问超出权限范围的数据资源\",\n            time: \"2024-01-15 18:20\",\n            severity: \"high\",\n            status: \"resolved\"\n        },\n        {\n            id: 3,\n            type: \"info\",\n            title: \"密码策略更新\",\n            description: \"系统密码策略已更新，要求更强的密码复杂度\",\n            time: \"2024-01-15 14:00\",\n            severity: \"low\",\n            status: \"completed\"\n        }\n    ];\n    // 数据质量数据\n    const qualityIssues = [\n        {\n            id: 1,\n            dataSource: \"人口基础信息库\",\n            issueType: \"数据缺失\",\n            description: \"身份证号码字段存在空值\",\n            severity: \"high\",\n            recordCount: 1247,\n            status: \"pending\",\n            reportTime: \"2024-01-15 14:30\",\n            assignee: \"张三\"\n        },\n        {\n            id: 2,\n            dataSource: \"企业注册信息库\",\n            issueType: \"格式错误\",\n            description: \"统一社会信用代码格式不规范\",\n            severity: \"medium\",\n            recordCount: 89,\n            status: \"correcting\",\n            reportTime: \"2024-01-15 10:15\",\n            assignee: \"李四\"\n        },\n        {\n            id: 3,\n            dataSource: \"地理信息数据库\",\n            issueType: \"重复数据\",\n            description: \"坐标信息存在重复记录\",\n            severity: \"low\",\n            recordCount: 23,\n            status: \"resolved\",\n            reportTime: \"2024-01-14 16:45\",\n            assignee: \"王五\"\n        }\n    ];\n    // 工单流转数据\n    const workflowTickets = [\n        {\n            id: \"WO-2024-001\",\n            title: \"人口数据质量问题处理\",\n            type: \"数据纠错\",\n            priority: \"high\",\n            status: \"in_progress\",\n            creator: \"数据管理员\",\n            assignee: \"技术支持组\",\n            createTime: \"2024-01-15 09:00\",\n            updateTime: \"2024-01-15 14:30\",\n            description: \"处理人口基础信息库中的数据质量问题\"\n        },\n        {\n            id: \"WO-2024-002\",\n            title: \"企业信息格式标准化\",\n            type: \"数据清洗\",\n            priority: \"medium\",\n            status: \"pending\",\n            creator: \"业务部门\",\n            assignee: \"待分配\",\n            createTime: \"2024-01-15 11:20\",\n            updateTime: \"2024-01-15 11:20\",\n            description: \"统一企业注册信息的数据格式标准\"\n        },\n        {\n            id: \"WO-2024-003\",\n            title: \"地理数据去重处理\",\n            type: \"数据优化\",\n            priority: \"low\",\n            status: \"completed\",\n            creator: \"系统管理员\",\n            assignee: \"数据处理组\",\n            createTime: \"2024-01-14 14:00\",\n            updateTime: \"2024-01-15 08:30\",\n            description: \"清理地理信息数据库中的重复记录\"\n        }\n    ];\n    // 质量报告数据\n    const qualityReports = [\n        {\n            id: 1,\n            name: \"2024年1月数据质量月报\",\n            type: \"月度报告\",\n            period: \"2024-01\",\n            generateTime: \"2024-01-15 18:00\",\n            status: \"published\",\n            qualityScore: 92.5,\n            issueCount: 156,\n            resolvedCount: 134\n        },\n        {\n            id: 2,\n            name: \"人口数据质量专项报告\",\n            type: \"专项报告\",\n            period: \"2024-01-01 至 2024-01-15\",\n            generateTime: \"2024-01-15 16:30\",\n            status: \"draft\",\n            qualityScore: 89.2,\n            issueCount: 89,\n            resolvedCount: 67\n        },\n        {\n            id: 3,\n            name: \"企业信息质量评估报告\",\n            type: \"评估报告\",\n            period: \"2024-01-10 至 2024-01-15\",\n            generateTime: \"2024-01-15 14:00\",\n            status: \"published\",\n            qualityScore: 95.8,\n            issueCount: 23,\n            resolvedCount: 23\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"passed\":\n                return \"text-green-600 bg-green-100\";\n            case \"warning\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"failed\":\n                return \"text-red-600 bg-red-100\";\n            case \"active\":\n                return \"text-green-600 bg-green-100\";\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"high\":\n                return \"text-red-600 bg-red-100\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"low\":\n                return \"text-blue-600 bg-blue-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-violet-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-violet-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/\",\n                                                className: \"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"返回首页\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-px h-6 bg-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: \"数据安全治理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Data Security Governance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"刷新数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-2 bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"导出报告\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-6 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"-mb-px flex space-x-8\",\n                                        children: [\n                                            {\n                                                id: \"overview\",\n                                                name: \"安全概览\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                            },\n                                            {\n                                                id: \"permissions\",\n                                                name: \"权限管理\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                                            },\n                                            {\n                                                id: \"compliance\",\n                                                name: \"合规检查\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                            },\n                                            {\n                                                id: \"quality\",\n                                                name: \"数据质量\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                            },\n                                            {\n                                                id: \"workflow\",\n                                                name: \"工单流转\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                            },\n                                            {\n                                                id: \"reports\",\n                                                name: \"质量报告\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                            },\n                                            {\n                                                id: \"events\",\n                                                name: \"安全事件\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                            },\n                                            {\n                                                id: \"settings\",\n                                                name: \"安全设置\",\n                                                icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                            }\n                                        ].map((tab)=>{\n                                            const Icon = tab.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"数据资产总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                                    children: securityOverview.totalAssets.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"安全资产\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-green-600\",\n                                                                    children: securityOverview.secureAssets.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"风险资产\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-red-600\",\n                                                                    children: securityOverview.riskAssets\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-red-500 to-rose-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"合规率\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-3xl font-bold text-blue-600\",\n                                                                    children: [\n                                                                        securityOverview.complianceRate,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-gray-900 mb-4\",\n                                                        children: \"安全事件趋势\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"安全事件趋势图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-gray-900 mb-4\",\n                                                        children: \"合规性评分\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"合规性评分图表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"permissions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"用户权限管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                                    children: \"添加用户\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"搜索用户、部门或角色...\",\n                                                            value: searchTerm,\n                                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"筛选\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                                    children: \"用户\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                                    children: \"部门\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                                    children: \"角色\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                                    children: \"权限\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                                    children: \"状态\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                                    children: \"最后访问\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                                    children: \"操作\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: permissionData.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"border-b border-gray-100 hover:bg-gray-50/50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-4 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white text-sm font-medium\",\n                                                                                        children: user.user[0]\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 485,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 484,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: user.user\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 487,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-4 px-4 text-gray-600\",\n                                                                        children: user.department\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-4 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                            children: user.role\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-4 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-1\",\n                                                                            children: user.permissions.map((permission, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                                                                                    children: permission\n                                                                                }, idx, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-4 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(user.status)),\n                                                                            children: user.status === \"active\" ? \"活跃\" : \"待审核\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-4 px-4 text-gray-600 text-sm\",\n                                                                        children: user.lastAccess\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-4 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                                    children: \"编辑\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-red-600 hover:text-red-800 text-sm\",\n                                                                                    children: \"删除\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, user.id, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"compliance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"合规检查项目\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                                    children: \"执行全面检查\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: complianceChecks.map((check)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: check.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(check.status)),\n                                                                    children: check.status === \"passed\" ? \"通过\" : check.status === \"warning\" ? \"警告\" : \"失败\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"评分\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-bold text-lg\",\n                                                                            children: [\n                                                                                check.score,\n                                                                                \"/100\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-2 rounded-full \".concat(check.score >= 90 ? \"bg-green-500\" : check.score >= 70 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                        style: {\n                                                                            width: \"\".concat(check.score, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: [\n                                                                                \"发现问题: \",\n                                                                                check.issues,\n                                                                                \" 个\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: [\n                                                                                \"最后检查: \",\n                                                                                check.lastCheck\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, check.id, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"quality\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"数据质量在线纠错\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                                    children: \"执行质量检查\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: qualityIssues.map((issue)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3 mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: issue.dataSource\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getSeverityColor(issue.severity)),\n                                                                                children: issue.severity === \"high\" ? \"高危\" : issue.severity === \"medium\" ? \"中危\" : \"低危\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(issue.status === \"resolved\" ? \"bg-green-100 text-green-800\" : issue.status === \"correcting\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                                                children: issue.status === \"resolved\" ? \"已解决\" : issue.status === \"correcting\" ? \"纠错中\" : \"待处理\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 596,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"问题类型\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 608,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: issue.issueType\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 609,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 607,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"影响记录数\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 612,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-red-600\",\n                                                                                        children: issue.recordCount.toLocaleString()\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 613,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 611,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"负责人\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 616,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: issue.assignee\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 617,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 615,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600 mb-1\",\n                                                                                children: \"问题描述\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-900\",\n                                                                                children: issue.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 623,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 627,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"发现时间: \",\n                                                                                    issue.reportTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                children: [\n                                                                    issue.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors\",\n                                                                        children: \"开始纠错\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    issue.status === \"correcting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors\",\n                                                                        children: \"完成纠错\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                        children: \"查看详情\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 632,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, issue.id, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"workflow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"工单流转管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                                    children: \"创建工单\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: workflowTickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3 mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                                children: ticket.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 670,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium\",\n                                                                                children: ticket.id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(ticket.priority === \"high\" ? \"bg-red-100 text-red-800\" : ticket.priority === \"medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-blue-100 text-blue-800\"),\n                                                                                children: ticket.priority === \"high\" ? \"高优先级\" : ticket.priority === \"medium\" ? \"中优先级\" : \"低优先级\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(ticket.status === \"completed\" ? \"bg-green-100 text-green-800\" : ticket.status === \"in_progress\" ? \"bg-blue-100 text-blue-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                                children: ticket.status === \"completed\" ? \"已完成\" : ticket.status === \"in_progress\" ? \"进行中\" : \"待处理\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 682,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"工单类型\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 694,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: ticket.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 695,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"创建人\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 698,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: ticket.creator\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 699,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"负责人\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 702,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: ticket.assignee\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 703,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 701,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600\",\n                                                                                        children: \"创建时间\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 706,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-gray-900\",\n                                                                                        children: ticket.createTime\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 707,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 705,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600 mb-1\",\n                                                                                children: \"工单描述\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 712,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-900\",\n                                                                                children: ticket.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 713,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 717,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"最后更新: \",\n                                                                                    ticket.updateTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 718,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 716,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 ml-4\",\n                                                                children: [\n                                                                    ticket.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors\",\n                                                                        children: \"接受工单\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 724,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    ticket.status === \"in_progress\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors\",\n                                                                        children: \"完成工单\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 729,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                        children: \"查看详情\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, ticket.id, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"reports\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"数据质量报告\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                                                    children: \"生成报告\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                            children: qualityReports.map((report)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                    children: report.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(report.status === \"published\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                                    children: report.status === \"published\" ? \"已发布\" : \"草稿\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"报告类型\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 769,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: report.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 770,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 768,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"统计周期\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 773,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: report.period\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 774,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 mb-2\",\n                                                                            children: \"质量评分\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 779,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1 bg-gray-200 rounded-full h-3\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-3 rounded-full \".concat(report.qualityScore >= 90 ? \"bg-green-500\" : report.qualityScore >= 70 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                        style: {\n                                                                                            width: \"\".concat(report.qualityScore, \"%\")\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 782,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 781,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-lg font-bold text-blue-600\",\n                                                                                    children: report.qualityScore\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 790,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 778,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"发现问题\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 796,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xl font-bold text-red-600\",\n                                                                                    children: report.issueCount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 797,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"已解决\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 800,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xl font-bold text-green-600\",\n                                                                                    children: report.resolvedCount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                    lineNumber: 801,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 799,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 794,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Calendar, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"生成时间: \",\n                                                                                report.generateTime\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                    children: \"查看报告\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-gray-600 hover:text-gray-800 text-sm\",\n                                                                    children: \"下载\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 813,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                report.status === \"draft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-800 text-sm\",\n                                                                    children: \"发布\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, report.id, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-6\",\n                                            children: \"安全事件监控\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: securityEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 rounded-full flex items-center justify-center \".concat(event.type === \"error\" ? \"bg-red-100\" : event.type === \"warning\" ? \"bg-yellow-100\" : \"bg-blue-100\"),\n                                                                        children: event.type === \"error\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-red-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 841,\n                                                                            columnNumber: 31\n                                                                        }, this) : event.type === \"warning\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-yellow-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 843,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 836,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-gray-900 mb-1\",\n                                                                                children: event.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 850,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mb-2\",\n                                                                                children: event.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 851,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"flex items-center space-x-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                                lineNumber: 854,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: event.time\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                                lineNumber: 855,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 853,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getSeverityColor(event.severity)),\n                                                                                        children: event.severity === \"high\" ? \"高危\" : event.severity === \"medium\" ? \"中危\" : \"低危\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                        lineNumber: 857,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                        lineNumber: 849,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(event.status === \"resolved\" ? \"bg-green-100 text-green-800\" : event.status === \"investigating\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-blue-100 text-blue-800\"),\n                                                                    children: event.status === \"resolved\" ? \"已解决\" : event.status === \"investigating\" ? \"调查中\" : \"已完成\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, event.id, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 831,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 828,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-6\",\n                                            children: \"安全策略配置\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"密码策略\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 897,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"要求复杂密码（包含大小写字母、数字、特殊字符）\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 901,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"密码最小长度 8 位\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 902,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 900,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"90 天强制更换密码\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 906,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"访问控制\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 919,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"启用双因素认证\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 920,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 923,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"限制同时登录设备数量\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 924,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 922,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 927,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"启用 IP 白名单\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 928,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 936,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"审计日志\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 941,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"记录所有数据访问操作\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 945,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"记录权限变更操作\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 946,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 944,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 949,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"日志保留 365 天\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 950,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 948,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowLeft_BarChart3_CheckCircle_Clock_Database_Download_FileCheck_FileText_Filter_Key_RefreshCw_Search_Settings_Shield_TrendingUp_UserCheck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 958,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"监控告警\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 957,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 963,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"异常访问行为告警\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 964,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 962,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            defaultChecked: true,\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 967,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"权限越权尝试告警\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 968,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"rounded border-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-700\",\n                                                                            children: \"大量数据导出告警\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                            lineNumber: 972,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                                        children: \"重置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                        children: \"保存设置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                                lineNumber: 979,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\security-governance\\\\page.tsx\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, this);\n}\n_s(SecurityGovernancePage, \"cplOGMBvOLR9NJFhNB8WNXh0geM=\");\n_c = SecurityGovernancePage;\nvar _c;\n$RefreshReg$(_c, \"SecurityGovernancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/security-governance/page.tsx\n"));

/***/ })

});