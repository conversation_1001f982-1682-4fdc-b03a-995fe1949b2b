"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HomePage() {\n    _s();\n    const [showModuleOverlay, setShowModuleOverlay] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showLifecycleModules, setShowLifecycleModules] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 业务模块数据\n    const businessModules = [\n        {\n            name: \"数据大屏\",\n            description: \"实时数据可视化展示，支持多种图表和指标监控\",\n            href: \"/screen\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            gradient: \"from-blue-500 to-cyan-500\",\n            logo: \"\\uD83D\\uDCCA\",\n            features: [\n                \"实时监控\",\n                \"可视化图表\",\n                \"大屏展示\"\n            ]\n        },\n        {\n            name: \"报表分析\",\n            description: \"多维度数据分析报表，支持自定义报表生成\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            gradient: \"from-green-500 to-emerald-500\",\n            logo: \"\\uD83D\\uDCC8\",\n            features: [\n                \"多维分析\",\n                \"自定义报表\",\n                \"数据导出\"\n            ]\n        },\n        {\n            name: \"数据采集\",\n            description: \"多源数据采集接入，支持实时和批量数据导入\",\n            href: \"/collection\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            gradient: \"from-purple-500 to-violet-500\",\n            logo: \"\\uD83D\\uDD04\",\n            features: [\n                \"多源接入\",\n                \"实时采集\",\n                \"数据清洗\"\n            ]\n        },\n        {\n            name: \"数据汇聚\",\n            description: \"跨系统数据整合汇聚，统一数据标准和格式\",\n            href: \"/aggregation\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            gradient: \"from-orange-500 to-amber-500\",\n            logo: \"\\uD83D\\uDD17\",\n            features: [\n                \"数据整合\",\n                \"标准化\",\n                \"质量控制\"\n            ]\n        },\n        {\n            name: \"数据治理\",\n            description: \"数据质量管控，数据清洗、去重、标准化处理\",\n            href: \"/governance\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            gradient: \"from-red-500 to-rose-500\",\n            logo: \"\\uD83D\\uDEE1️\",\n            features: [\n                \"质量管控\",\n                \"数据清洗\",\n                \"合规管理\"\n            ]\n        },\n        {\n            name: \"资源池管理\",\n            description: \"数据资源统一管理，资源目录和权限控制\",\n            href: \"/resources\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            gradient: \"from-indigo-500 to-blue-500\",\n            logo: \"\\uD83D\\uDCBE\",\n            features: [\n                \"资源目录\",\n                \"权限管理\",\n                \"存储优化\"\n            ]\n        },\n        {\n            name: \"设备监控\",\n            description: \"服务器设备实时监控，性能指标和告警管理\",\n            href: \"/monitoring\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            gradient: \"from-teal-500 to-cyan-500\",\n            logo: \"\\uD83D\\uDDA5️\",\n            features: [\n                \"实时监控\",\n                \"性能分析\",\n                \"告警管理\"\n            ]\n        },\n        {\n            name: \"网络管理\",\n            description: \"多物理网络管理，网络拓扑和流量监控\",\n            href: \"/network\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            gradient: \"from-pink-500 to-rose-500\",\n            logo: \"\\uD83C\\uDF10\",\n            features: [\n                \"网络拓扑\",\n                \"流量监控\",\n                \"安全管理\"\n            ]\n        }\n    ];\n    // 核心能力\n    const capabilities = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"数据全生命周期管理\",\n            description: \"从数据采集、清洗、存储到应用的全流程管理，确保数据质量和安全性\",\n            gradient: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"智能数据分析\",\n            description: \"多维度数据分析和可视化展示，支持实时监控和预测分析\",\n            gradient: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"数据安全治理\",\n            description: \"完善的数据安全体系和质量管控机制，保障数据合规使用\",\n            gradient: \"from-purple-500 to-violet-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"跨部门数据共享\",\n            description: \"打破数据孤岛，实现政务数据统一共享和协同应用\",\n            gradient: \"from-orange-500 to-red-500\"\n        }\n    ];\n    // 解决的场景\n    const scenarios = [\n        {\n            title: \"政府决策支撑\",\n            description: \"为领导层提供实时、准确的数据分析，支撑科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            title: \"跨部门协同\",\n            description: \"消除信息孤岛，实现部门间数据无缝对接和业务协同\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            title: \"公共服务优化\",\n            description: \"通过数据分析优化公共服务流程，提升市民满意度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            title: \"监管效能提升\",\n            description: \"利用大数据技术提升监管效率和精准度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ];\n    // 数据来源单位\n    const dataSourceUnits = [\n        \"市政府办公厅\",\n        \"发展改革委\",\n        \"教育局\",\n        \"科技局\",\n        \"工信局\",\n        \"公安局\",\n        \"民政局\",\n        \"司法局\",\n        \"财政局\",\n        \"人社局\",\n        \"自然资源局\",\n        \"生态环境局\",\n        \"住建局\",\n        \"交通运输局\",\n        \"水务局\",\n        \"农业农村局\",\n        \"商务局\",\n        \"文旅局\",\n        \"卫健委\",\n        \"应急管理局\",\n        \"审计局\",\n        \"市场监管局\",\n        \"统计局\",\n        \"医保局\"\n    ];\n    // 权威数据领域\n    const authorityData = [\n        {\n            area: \"人口信息\",\n            coverage: \"100%\",\n            source: \"公安、民政、人社等部门\",\n            color: \"blue\"\n        },\n        {\n            area: \"企业信息\",\n            coverage: \"100%\",\n            source: \"市场监管、税务、工信等部门\",\n            color: \"green\"\n        },\n        {\n            area: \"地理信息\",\n            coverage: \"100%\",\n            source: \"自然资源、住建、交通等部门\",\n            color: \"purple\"\n        },\n        {\n            area: \"经济数据\",\n            coverage: \"95%\",\n            source: \"统计、财政、发改等部门\",\n            color: \"orange\"\n        },\n        {\n            area: \"社会事业\",\n            coverage: \"90%\",\n            source: \"教育、卫健、文旅等部门\",\n            color: \"pink\"\n        },\n        {\n            area: \"环境数据\",\n            coverage: \"100%\",\n            source: \"生态环境、水务、应急等部门\",\n            color: \"teal\"\n        }\n    ];\n    // 目标客户\n    const targetUsers = [\n        {\n            type: \"政府决策层\",\n            desc: \"为各级领导提供数据支撑和分析报告，辅助科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            features: [\n                \"实时数据大屏\",\n                \"决策分析报告\",\n                \"趋势预测分析\"\n            ]\n        },\n        {\n            type: \"业务部门\",\n            desc: \"各委办局日常业务数据管理和跨部门协同应用\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            features: [\n                \"业务数据管理\",\n                \"跨部门协同\",\n                \"流程优化\"\n            ]\n        },\n        {\n            type: \"技术人员\",\n            desc: \"数据开发、运维和技术支持人员的专业工具\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            features: [\n                \"数据开发工具\",\n                \"系统监控\",\n                \"技术支持\"\n            ]\n        },\n        {\n            type: \"公众服务\",\n            desc: \"为市民和企业提供便民服务和信息查询\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            features: [\n                \"信息查询\",\n                \"在线服务\",\n                \"便民应用\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-6 py-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center mb-20 animate-fade-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-blue-50/50 backdrop-blur-sm px-6 py-3 rounded-full mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-medium text-blue-700\",\n                                                children: \"智慧政务 \\xb7 数据驱动 \\xb7 创新未来\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-6xl md:text-7xl font-bold mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                            children: \"云宇政数平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"统一的政务数据管理与服务平台，为政府数字化转型提供全方位的数据支撑， 实现跨部门数据共享、智能分析决策和高效政务服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-8 mb-12 text-lg text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"24个委办局接入\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"2.4TB数据存储\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"99.9%系统可用性\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: showModuleOverlay ? \"业务模块快速入口\" : \"平台核心能力\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: showModuleOverlay ? \"选择您需要的功能模块，直接进入对应的业务操作界面\" : \"为政务数字化转型提供全方位的技术支撑\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    !showModuleOverlay ? // 原核心能力内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                                        children: [\n                                            capabilities.map((capability, index)=>{\n                                                const Icon = capability.icon;\n                                                const isLifecycleCard = capability.title === \"数据全生命周期管理\";\n                                                const isSecurityCard = capability.title === \"数据安全治理\";\n                                                const isSharingCard = capability.title === \"跨部门数据共享\";\n                                                const handleCardClick = ()=>{\n                                                    if (isSecurityCard) {\n                                                        window.location.href = \"/security-governance\";\n                                                    } else if (isSharingCard) {\n                                                        window.location.href = \"/data-sharing\";\n                                                    }\n                                                };\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20 \".concat(isSecurityCard || isSharingCard ? \"cursor-pointer\" : \"\"),\n                                                            style: {\n                                                                animationDelay: \"\".concat(index * 100, \"ms\")\n                                                            },\n                                                            onMouseEnter: ()=>{\n                                                                if (isLifecycleCard) {\n                                                                    setShowLifecycleModules(true);\n                                                                }\n                                                            },\n                                                            onMouseLeave: ()=>{\n                                                                if (isLifecycleCard) {\n                                                                    setShowLifecycleModules(false);\n                                                                }\n                                                            },\n                                                            onClick: handleCardClick,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-br \".concat(capability.gradient, \" rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                                        children: capability.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 leading-relaxed\",\n                                                                        children: capability.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isLifecycleCard && showLifecycleModules && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-full top-0 ml-4 z-50 animate-fade-in\",\n                                                            onMouseEnter: ()=>setShowLifecycleModules(true),\n                                                            onMouseLeave: ()=>setShowLifecycleModules(false),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-4 w-80\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: \"/collection\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                            className: \"w-6 h-6 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 318,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 317,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"text-lg font-bold text-gray-900 group-hover:text-purple-700 transition-colors\",\n                                                                                                children: \"数据采集\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 321,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: \"多源数据采集接入\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 322,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-gray-400 group-hover:text-purple-600 transform group-hover:translate-x-1 transition-all\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: \"/aggregation\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                            className: \"w-6 h-6 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 334,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 333,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"text-lg font-bold text-gray-900 group-hover:text-orange-700 transition-colors\",\n                                                                                                children: \"数据汇聚\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 337,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: \"跨系统数据整合\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 338,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 336,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-gray-400 group-hover:text-orange-600 transform group-hover:translate-x-1 transition-all\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 340,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: \"/governance\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-12 h-12 bg-gradient-to-br from-red-500 to-rose-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                            className: \"w-6 h-6 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 350,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 349,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"text-lg font-bold text-gray-900 group-hover:text-red-700 transition-colors\",\n                                                                                                children: \"数据治理\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 353,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: \"数据质量管控\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 354,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 352,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-gray-400 group-hover:text-red-600 transform group-hover:translate-x-1 transition-all\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 356,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, capability.title, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 21\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/dashboard\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 group border border-blue-300/50 cursor-pointer transform hover:scale-105\",\n                                                        style: {\n                                                            animationDelay: \"400ms\"\n                                                        },\n                                                        onMouseEnter: ()=>setShowModuleOverlay(true),\n                                                        onMouseLeave: ()=>setShowModuleOverlay(false),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"w-8 h-8 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-white mb-4\",\n                                                                    children: \"立即体验\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-100 leading-relaxed mb-4\",\n                                                                    children: \"悬停查看业务模块，点击进入云宇政数平台\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white font-bold flex items-center group-hover:translate-x-1 transition-transform\",\n                                                                        children: [\n                                                                            \"进入平台\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-5 h-5 ml-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this) : // 业务模块内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-fade-in\",\n                                        onMouseEnter: ()=>setShowModuleOverlay(true),\n                                        onMouseLeave: ()=>setShowModuleOverlay(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                children: businessModules.map((module, index)=>{\n                                                    const Icon = module.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: module.href,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer border border-white/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-br \".concat(module.gradient, \" rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center space-x-2 mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-2xl\",\n                                                                                children: module.logo\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-lg font-bold text-gray-900 group-hover:text-blue-700 transition-colors\",\n                                                                                children: module.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                        children: module.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap justify-center gap-1 mb-3\",\n                                                                        children: module.features.slice(0, 3).map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\",\n                                                                                children: feature\n                                                                            }, idx, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 422,\n                                                                                columnNumber: 33\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center text-blue-600 group-hover:text-blue-700 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: \"进入系统\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, module.name, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8 text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/dashboard\",\n                                                    className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"进入主控台\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"200ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"解决的应用场景\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖政务管理的各个关键环节，提升政府治理效能\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                        children: scenarios.map((scenario, index)=>{\n                                            const Icon = scenario.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                                    children: scenario.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 leading-relaxed\",\n                                                                    children: scenario.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, scenario.title, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"400ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"数据来源单位\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖全市24个主要委办局，构建统一的数据生态体系\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6\",\n                                            children: dataSourceUnits.map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 rounded-xl bg-gray-50/50 hover:bg-blue-50/50 transition-all duration-300 group\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400 group-hover:text-blue-500 mx-auto mb-3 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors\",\n                                                            children: unit\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, unit, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"600ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"权威数据覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"全面覆盖政务核心数据领域，确保数据权威性和完整性\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: authorityData.map((data, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900\",\n                                                                children: data.area\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl font-bold text-blue-600\",\n                                                                children: data.coverage\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6\",\n                                                        children: data.source\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-1000\",\n                                                            style: {\n                                                                width: data.coverage\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, data.area, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"800ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"服务对象\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"为不同类型用户提供专业化的数据服务和解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                        children: targetUsers.map((user, index)=>{\n                                            const Icon = user.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group text-center border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl mx-auto mb-6 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                        children: user.type\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6 leading-relaxed\",\n                                                        children: user.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: user.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    feature\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, user.type, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center animate-slide-up\",\n                                style: {\n                                    animationDelay: \"1200ms\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white shadow-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold mb-6\",\n                                            children: \"准备好开始了吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl mb-12 text-blue-100\",\n                                            children: \"立即体验云宇政数平台，开启您的智慧政务数据管理之旅\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/dashboard\",\n                                                    className: \"bg-white text-blue-600 hover:bg-blue-50 font-bold py-5 px-12 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"进入平台\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-500/20 backdrop-blur-sm text-white hover:bg-blue-500/30 font-medium py-5 px-12 rounded-2xl transition-all duration-300 border border-white/20 flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"了解更多\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white/80 backdrop-blur-xl border-t border-white/20 mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg mb-3\",\n                                        children: \"\\xa9 2024 云宇政数平台. 保留所有权利.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base\",\n                                        children: \"为政府数字化转型提供专业的数据管理服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"CxmuX3o+PN3XC6UILmiLyOd/Dl4=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});