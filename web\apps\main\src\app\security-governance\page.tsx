'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  Shield,
  Lock,
  Eye,
  Users,
  FileCheck,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  Settings,
  Search,
  Filter,
  Download,
  RefreshCw,
  ArrowLeft,
  Database,
  Key,
  UserCheck,
  FileText,
  Activity,
  TrendingUp,
  AlertCircle,
  Zap
} from 'lucide-react'

export default function SecurityGovernancePage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')

  // 安全概览数据
  const securityOverview = {
    totalAssets: 1247,
    secureAssets: 1189,
    riskAssets: 58,
    complianceRate: 95.3,
    lastScan: '2024-01-15 14:30:00'
  }

  // 权限管理数据
  const permissionData = [
    {
      id: 1,
      user: '张三',
      department: '市政府办公厅',
      role: '数据管理员',
      permissions: ['数据查看', '数据导出', '权限管理'],
      status: 'active',
      lastAccess: '2024-01-15 10:30'
    },
    {
      id: 2,
      user: '李四',
      department: '发展改革委',
      role: '业务用户',
      permissions: ['数据查看'],
      status: 'active',
      lastAccess: '2024-01-15 09:15'
    },
    {
      id: 3,
      user: '王五',
      department: '教育局',
      role: '数据分析师',
      permissions: ['数据查看', '数据分析'],
      status: 'pending',
      lastAccess: '2024-01-14 16:45'
    }
  ]

  // 合规检查数据
  const complianceChecks = [
    {
      id: 1,
      name: '个人信息保护检查',
      status: 'passed',
      score: 98,
      lastCheck: '2024-01-15 08:00',
      issues: 2
    },
    {
      id: 2,
      name: '数据访问权限审计',
      status: 'warning',
      score: 85,
      lastCheck: '2024-01-15 06:00',
      issues: 8
    },
    {
      id: 3,
      name: '数据传输加密检查',
      status: 'passed',
      score: 100,
      lastCheck: '2024-01-15 04:00',
      issues: 0
    },
    {
      id: 4,
      name: '数据备份完整性检查',
      status: 'failed',
      score: 65,
      lastCheck: '2024-01-15 02:00',
      issues: 15
    }
  ]

  // 安全事件数据
  const securityEvents = [
    {
      id: 1,
      type: 'warning',
      title: '异常数据访问',
      description: '检测到用户在非工作时间大量访问敏感数据',
      time: '2024-01-15 23:45',
      severity: 'medium',
      status: 'investigating'
    },
    {
      id: 2,
      type: 'error',
      title: '权限越权尝试',
      description: '用户尝试访问超出权限范围的数据资源',
      time: '2024-01-15 18:20',
      severity: 'high',
      status: 'resolved'
    },
    {
      id: 3,
      type: 'info',
      title: '密码策略更新',
      description: '系统密码策略已更新，要求更强的密码复杂度',
      time: '2024-01-15 14:00',
      severity: 'low',
      status: 'completed'
    }
  ]

  // 数据质量数据
  const qualityIssues = [
    {
      id: 1,
      dataSource: '人口基础信息库',
      issueType: '数据缺失',
      description: '身份证号码字段存在空值',
      severity: 'high',
      recordCount: 1247,
      status: 'pending',
      reportTime: '2024-01-15 14:30',
      assignee: '张三'
    },
    {
      id: 2,
      dataSource: '企业注册信息库',
      issueType: '格式错误',
      description: '统一社会信用代码格式不规范',
      severity: 'medium',
      recordCount: 89,
      status: 'correcting',
      reportTime: '2024-01-15 10:15',
      assignee: '李四'
    },
    {
      id: 3,
      dataSource: '地理信息数据库',
      issueType: '重复数据',
      description: '坐标信息存在重复记录',
      severity: 'low',
      recordCount: 23,
      status: 'resolved',
      reportTime: '2024-01-14 16:45',
      assignee: '王五'
    }
  ]

  // 工单流转数据
  const workflowTickets = [
    {
      id: 'WO-2024-001',
      title: '人口数据质量问题处理',
      type: '数据纠错',
      priority: 'high',
      status: 'in_progress',
      creator: '数据管理员',
      assignee: '技术支持组',
      createTime: '2024-01-15 09:00',
      updateTime: '2024-01-15 14:30',
      description: '处理人口基础信息库中的数据质量问题'
    },
    {
      id: 'WO-2024-002',
      title: '企业信息格式标准化',
      type: '数据清洗',
      priority: 'medium',
      status: 'pending',
      creator: '业务部门',
      assignee: '待分配',
      createTime: '2024-01-15 11:20',
      updateTime: '2024-01-15 11:20',
      description: '统一企业注册信息的数据格式标准'
    },
    {
      id: 'WO-2024-003',
      title: '地理数据去重处理',
      type: '数据优化',
      priority: 'low',
      status: 'completed',
      creator: '系统管理员',
      assignee: '数据处理组',
      createTime: '2024-01-14 14:00',
      updateTime: '2024-01-15 08:30',
      description: '清理地理信息数据库中的重复记录'
    }
  ]

  // 质量报告数据
  const qualityReports = [
    {
      id: 1,
      name: '2024年1月数据质量月报',
      type: '月度报告',
      period: '2024-01',
      generateTime: '2024-01-15 18:00',
      status: 'published',
      qualityScore: 92.5,
      issueCount: 156,
      resolvedCount: 134
    },
    {
      id: 2,
      name: '人口数据质量专项报告',
      type: '专项报告',
      period: '2024-01-01 至 2024-01-15',
      generateTime: '2024-01-15 16:30',
      status: 'draft',
      qualityScore: 89.2,
      issueCount: 89,
      resolvedCount: 67
    },
    {
      id: 3,
      name: '企业信息质量评估报告',
      type: '评估报告',
      period: '2024-01-10 至 2024-01-15',
      generateTime: '2024-01-15 14:00',
      status: 'published',
      qualityScore: 95.8,
      issueCount: 23,
      resolvedCount: 23
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'failed': return 'text-red-600 bg-red-100'
      case 'active': return 'text-green-600 bg-green-100'
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-violet-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-violet-400/20 to-purple-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10">
        {/* 头部导航 */}
        <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                  <ArrowLeft className="w-5 h-5" />
                  <span>返回首页</span>
                </Link>
                <div className="w-px h-6 bg-gray-300"></div>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">数据安全治理</h1>
                    <p className="text-sm text-gray-600">Data Security Governance</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  <RefreshCw className="w-4 h-4" />
                  <span>刷新数据</span>
                </button>
                <button className="flex items-center space-x-2 bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <Download className="w-4 h-4" />
                  <span>导出报告</span>
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* 主要内容 */}
        <main className="max-w-7xl mx-auto px-6 py-8">
          {/* 标签页导航 */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'overview', name: '安全概览', icon: BarChart3 },
                  { id: 'permissions', name: '权限管理', icon: Users },
                  { id: 'compliance', name: '合规检查', icon: FileCheck },
                  { id: 'quality', name: '数据质量', icon: Database },
                  { id: 'workflow', name: '工单流转', icon: RefreshCw },
                  { id: 'reports', name: '质量报告', icon: FileText },
                  { id: 'events', name: '安全事件', icon: AlertTriangle },
                  { id: 'settings', name: '安全设置', icon: Settings }
                ].map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{tab.name}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* 安全概览 */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* 关键指标卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">数据资产总数</p>
                      <p className="text-3xl font-bold text-gray-900">{securityOverview.totalAssets.toLocaleString()}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                      <Database className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">安全资产</p>
                      <p className="text-3xl font-bold text-green-600">{securityOverview.secureAssets.toLocaleString()}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                      <CheckCircle className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">风险资产</p>
                      <p className="text-3xl font-bold text-red-600">{securityOverview.riskAssets}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-rose-500 rounded-xl flex items-center justify-center">
                      <AlertTriangle className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">合规率</p>
                      <p className="text-3xl font-bold text-blue-600">{securityOverview.complianceRate}%</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center">
                      <FileCheck className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 安全趋势图表 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">安全事件趋势</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <TrendingUp className="w-12 h-12 mx-auto mb-2" />
                      <p>安全事件趋势图表</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">合规性评分</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                      <p>合规性评分图表</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 权限管理 */}
          {activeTab === 'permissions' && (
            <div className="space-y-6">
              {/* 搜索和过滤 */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-gray-900">用户权限管理</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    添加用户
                  </button>
                </div>

                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1 relative">
                    <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="搜索用户、部门或角色..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <Filter className="w-4 h-4" />
                    <span>筛选</span>
                  </button>
                </div>

                {/* 权限列表 */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-600">用户</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">部门</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">角色</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">权限</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">状态</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">最后访问</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {permissionData.map((user) => (
                        <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50/50">
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-sm font-medium">{user.user[0]}</span>
                              </div>
                              <span className="font-medium text-gray-900">{user.user}</span>
                            </div>
                          </td>
                          <td className="py-4 px-4 text-gray-600">{user.department}</td>
                          <td className="py-4 px-4">
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                              {user.role}
                            </span>
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex flex-wrap gap-1">
                              {user.permissions.map((permission, idx) => (
                                <span key={idx} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                  {permission}
                                </span>
                              ))}
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                              {user.status === 'active' ? '活跃' : '待审核'}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-gray-600 text-sm">{user.lastAccess}</td>
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-2">
                              <button className="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                              <button className="text-red-600 hover:text-red-800 text-sm">删除</button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* 合规检查 */}
          {activeTab === 'compliance' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">合规检查项目</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    执行全面检查
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {complianceChecks.map((check) => (
                    <div key={check.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-gray-900">{check.name}</h4>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(check.status)}`}>
                          {check.status === 'passed' ? '通过' : check.status === 'warning' ? '警告' : '失败'}
                        </span>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">评分</span>
                          <span className="font-bold text-lg">{check.score}/100</span>
                        </div>

                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              check.score >= 90 ? 'bg-green-500' :
                              check.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${check.score}%` }}
                          ></div>
                        </div>

                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">发现问题: {check.issues} 个</span>
                          <span className="text-gray-600">最后检查: {check.lastCheck}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 数据质量 */}
          {activeTab === 'quality' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">数据质量在线纠错</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    执行质量检查
                  </button>
                </div>

                <div className="space-y-4">
                  {qualityIssues.map((issue) => (
                    <div key={issue.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">{issue.dataSource}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(issue.severity)}`}>
                              {issue.severity === 'high' ? '高危' : issue.severity === 'medium' ? '中危' : '低危'}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              issue.status === 'resolved' ? 'bg-green-100 text-green-800' :
                              issue.status === 'correcting' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {issue.status === 'resolved' ? '已解决' :
                               issue.status === 'correcting' ? '纠错中' : '待处理'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                              <p className="text-sm text-gray-600">问题类型</p>
                              <p className="font-medium text-gray-900">{issue.issueType}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">影响记录数</p>
                              <p className="font-medium text-red-600">{issue.recordCount.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">负责人</p>
                              <p className="font-medium text-gray-900">{issue.assignee}</p>
                            </div>
                          </div>

                          <div className="mb-4">
                            <p className="text-sm text-gray-600 mb-1">问题描述</p>
                            <p className="text-gray-900">{issue.description}</p>
                          </div>

                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Clock className="w-4 h-4" />
                            <span>发现时间: {issue.reportTime}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          {issue.status === 'pending' && (
                            <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                              开始纠错
                            </button>
                          )}
                          {issue.status === 'correcting' && (
                            <button className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
                              完成纠错
                            </button>
                          )}
                          <button className="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 工单流转 */}
          {activeTab === 'workflow' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">工单流转管理</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    创建工单
                  </button>
                </div>

                <div className="space-y-4">
                  {workflowTickets.map((ticket) => (
                    <div key={ticket.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">{ticket.title}</h4>
                            <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium">
                              {ticket.id}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              ticket.priority === 'high' ? 'bg-red-100 text-red-800' :
                              ticket.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {ticket.priority === 'high' ? '高优先级' :
                               ticket.priority === 'medium' ? '中优先级' : '低优先级'}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              ticket.status === 'completed' ? 'bg-green-100 text-green-800' :
                              ticket.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {ticket.status === 'completed' ? '已完成' :
                               ticket.status === 'in_progress' ? '进行中' : '待处理'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                            <div>
                              <p className="text-sm text-gray-600">工单类型</p>
                              <p className="font-medium text-gray-900">{ticket.type}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">创建人</p>
                              <p className="font-medium text-gray-900">{ticket.creator}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">负责人</p>
                              <p className="font-medium text-gray-900">{ticket.assignee}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">创建时间</p>
                              <p className="font-medium text-gray-900">{ticket.createTime}</p>
                            </div>
                          </div>

                          <div className="mb-4">
                            <p className="text-sm text-gray-600 mb-1">工单描述</p>
                            <p className="text-gray-900">{ticket.description}</p>
                          </div>

                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <RefreshCw className="w-4 h-4" />
                            <span>最后更新: {ticket.updateTime}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          {ticket.status === 'pending' && (
                            <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                              接受工单
                            </button>
                          )}
                          {ticket.status === 'in_progress' && (
                            <button className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
                              完成工单
                            </button>
                          )}
                          <button className="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 质量报告 */}
          {activeTab === 'reports' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">数据质量报告</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    生成报告
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {qualityReports.map((report) => (
                    <div key={report.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900">{report.name}</h4>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          report.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {report.status === 'published' ? '已发布' : '草稿'}
                        </span>
                      </div>

                      <div className="space-y-3 mb-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">报告类型</p>
                            <p className="font-medium text-gray-900">{report.type}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">统计周期</p>
                            <p className="font-medium text-gray-900">{report.period}</p>
                          </div>
                        </div>

                        <div>
                          <p className="text-sm text-gray-600 mb-2">质量评分</p>
                          <div className="flex items-center space-x-3">
                            <div className="flex-1 bg-gray-200 rounded-full h-3">
                              <div
                                className={`h-3 rounded-full ${
                                  report.qualityScore >= 90 ? 'bg-green-500' :
                                  report.qualityScore >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${report.qualityScore}%` }}
                              ></div>
                            </div>
                            <span className="text-lg font-bold text-blue-600">{report.qualityScore}</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">发现问题</p>
                            <p className="text-xl font-bold text-red-600">{report.issueCount}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">已解决</p>
                            <p className="text-xl font-bold text-green-600">{report.resolvedCount}</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Calendar className="w-4 h-4" />
                          <span>生成时间: {report.generateTime}</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-end space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm">查看报告</button>
                        <button className="text-gray-600 hover:text-gray-800 text-sm">下载</button>
                        {report.status === 'draft' && (
                          <button className="text-green-600 hover:text-green-800 text-sm">发布</button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 安全事件 */}
          {activeTab === 'events' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <h3 className="text-lg font-bold text-gray-900 mb-6">安全事件监控</h3>

                <div className="space-y-4">
                  {securityEvents.map((event) => (
                    <div key={event.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            event.type === 'error' ? 'bg-red-100' :
                            event.type === 'warning' ? 'bg-yellow-100' : 'bg-blue-100'
                          }`}>
                            {event.type === 'error' ? (
                              <XCircle className="w-5 h-5 text-red-600" />
                            ) : event.type === 'warning' ? (
                              <AlertTriangle className="w-5 h-5 text-yellow-600" />
                            ) : (
                              <CheckCircle className="w-5 h-5 text-blue-600" />
                            )}
                          </div>

                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-1">{event.title}</h4>
                            <p className="text-gray-600 mb-2">{event.description}</p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span className="flex items-center space-x-1">
                                <Clock className="w-4 h-4" />
                                <span>{event.time}</span>
                              </span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>
                                {event.severity === 'high' ? '高危' : event.severity === 'medium' ? '中危' : '低危'}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            event.status === 'resolved' ? 'bg-green-100 text-green-800' :
                            event.status === 'investigating' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {event.status === 'resolved' ? '已解决' :
                             event.status === 'investigating' ? '调查中' : '已完成'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 安全设置 */}
          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <h3 className="text-lg font-bold text-gray-900 mb-6">安全策略配置</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* 密码策略 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                      <Key className="w-5 h-5" />
                      <span>密码策略</span>
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">要求复杂密码（包含大小写字母、数字、特殊字符）</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">密码最小长度 8 位</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">90 天强制更换密码</span>
                      </label>
                    </div>
                  </div>

                  {/* 访问控制 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                      <UserCheck className="w-5 h-5" />
                      <span>访问控制</span>
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">启用双因素认证</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">限制同时登录设备数量</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">启用 IP 白名单</span>
                      </label>
                    </div>
                  </div>

                  {/* 审计日志 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                      <FileText className="w-5 h-5" />
                      <span>审计日志</span>
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">记录所有数据访问操作</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">记录权限变更操作</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">日志保留 365 天</span>
                      </label>
                    </div>
                  </div>

                  {/* 监控告警 */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center space-x-2">
                      <Activity className="w-5 h-5" />
                      <span>监控告警</span>
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">异常访问行为告警</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">权限越权尝试告警</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="checkbox" className="rounded border-gray-300" />
                        <span className="text-sm text-gray-700">大量数据导出告警</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="flex items-center justify-end space-x-4">
                    <button className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                      重置
                    </button>
                    <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      保存设置
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
